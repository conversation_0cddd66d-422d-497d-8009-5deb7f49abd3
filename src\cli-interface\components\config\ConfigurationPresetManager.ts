import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';

interface ErrorHandlingPreset {
  name: string;
  description: string;
  config: {
    retry?: any;
    circuitBreaker?: any;
    enableErrorLogging?: boolean;
    enableMetrics?: boolean;
    showRetryProgress?: boolean;
    allowRetryCancel?: boolean;
  };
}

export class ConfigurationPresetManager extends BaseComponent {
  private configManager: ConfigManager;
  private presets: ErrorHandlingPreset[] = [
    {
      name: 'Conservative',
      description: 'Minimal retries, quick failure detection - for fast operations',
      config: {
        retry: {
          maxAttempts: 2,
          baseDelay: 500,
          maxDelay: 5000,
          backoffMultiplier: 1.5,
          backoffStrategy: 'exponential',
          jitterType: 'full',
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 3,
          recoveryTimeout: 15000,
          successThreshold: 1,
          monitoringWindow: 30000,
        },
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: false,
        allowRetryCancel: false,
      },
    },
    {
      name: 'Balanced',
      description: 'Standard configuration - good for most applications',
      config: {
        retry: {
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 60000,
          backoffMultiplier: 2,
          backoffStrategy: 'exponential',
          jitterType: 'full',
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 5,
          recoveryTimeout: 30000,
          successThreshold: 2,
          monitoringWindow: 60000,
        },
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: true,
        allowRetryCancel: true,
      },
    },
    {
      name: 'Aggressive',
      description: 'Maximum retries and resilience - for critical operations',
      config: {
        retry: {
          maxAttempts: 5,
          baseDelay: 2000,
          maxDelay: 120000,
          backoffMultiplier: 2.5,
          backoffStrategy: 'exponential',
          jitterType: 'decorrelated',
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 8,
          recoveryTimeout: 60000,
          successThreshold: 3,
          monitoringWindow: 120000,
        },
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: true,
        allowRetryCancel: true,
      },
    },
    {
      name: 'Development',
      description: 'Fast feedback for development - minimal delays',
      config: {
        retry: {
          maxAttempts: 1,
          baseDelay: 100,
          maxDelay: 1000,
          backoffMultiplier: 1,
          backoffStrategy: 'fixed',
          jitterType: 'none',
        },
        circuitBreaker: {
          enabled: false,
          failureThreshold: 10,
          recoveryTimeout: 5000,
          successThreshold: 1,
          monitoringWindow: 10000,
        },
        enableErrorLogging: true,
        enableMetrics: false,
        showRetryProgress: false,
        allowRetryCancel: false,
      },
    },
    {
      name: 'High-Throughput',
      description: 'Optimized for high-volume operations with quick recovery',
      config: {
        retry: {
          maxAttempts: 3,
          baseDelay: 200,
          maxDelay: 10000,
          backoffMultiplier: 1.8,
          backoffStrategy: 'exponential',
          jitterType: 'equal',
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 10,
          recoveryTimeout: 10000,
          successThreshold: 5,
          monitoringWindow: 30000,
          failureRateThreshold: 30,
          minimumRequests: 20,
        },
        enableErrorLogging: false,
        enableMetrics: true,
        showRetryProgress: false,
        allowRetryCancel: false,
      },
    },
  ];

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📋 Configuration Presets', 'Manage and apply error handling configuration presets');

    await this.showPresetMenu();
  }

  private async showPresetMenu(): Promise<void> {
    const choices = [
      { name: '📋 View Available Presets', value: 'view' },
      { name: '⚡ Apply Preset', value: 'apply' },
      { name: '💾 Save Current as Preset', value: 'save' },
      { name: '🗑️ Delete Custom Preset', value: 'delete' },
      { name: '📊 Compare Presets', value: 'compare' },
      { name: '📤 Export Preset', value: 'export' },
      { name: '📥 Import Preset', value: 'import' },
      { name: '🔙 Back to Error Handling Config', value: 'back' },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices,
      },
    ]);

    switch (action) {
      case 'view':
        await this.viewPresets();
        break;
      case 'apply':
        await this.applyPreset();
        break;
      case 'save':
        await this.saveCurrentAsPreset();
        break;
      case 'delete':
        await this.deleteCustomPreset();
        break;
      case 'compare':
        await this.comparePresets();
        break;
      case 'export':
        await this.exportPreset();
        break;
      case 'import':
        await this.importPreset();
        break;
      case 'back':
        return;
    }

    await this.showPresetMenu();
  }

  private async viewPresets(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📋 Available Configuration Presets');

    this.utils.showInfo('Built-in Presets:');
    console.log();

    this.presets.forEach((preset, index) => {
      console.log(`${index + 1}. ${this.utils.colorize(preset.name, this.config.theme.primary)}`);
      console.log(`   ${preset.description}`);
      console.log(`   • Max Retries: ${preset.config.retry?.maxAttempts || 'N/A'}`);
      console.log(`   • Circuit Breaker: ${preset.config.circuitBreaker?.enabled ? 'Enabled' : 'Disabled'}`);
      console.log(`   • Progress Display: ${preset.config.showRetryProgress ? 'Yes' : 'No'}`);
      console.log();
    });

    // Show custom presets if any exist
    const customPresets = await this.getCustomPresets();
    if (customPresets.length > 0) {
      console.log('Custom Presets:');
      customPresets.forEach((preset, index) => {
        console.log(`${index + 1}. ${this.utils.colorize(preset.name, this.config.theme.secondary)}`);
        console.log(`   ${preset.description}`);
        console.log();
      });
    }

    await this.utils.waitForKeyPress();
  }

  private async applyPreset(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('⚡ Apply Configuration Preset');

    const allPresets = [...this.presets, ...(await this.getCustomPresets())];
    
    const choices = allPresets.map(preset => ({
      name: `${preset.name} - ${preset.description}`,
      value: preset,
    }));

    const { selectedPreset } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedPreset',
        message: 'Select a preset to apply:',
        choices,
      },
    ]);

    // Show preview of what will be applied
    this.utils.showInfo('Preview of configuration changes:');
    console.log();
    this.displayPresetConfig(selectedPreset);
    console.log();

    const confirmed = await this.confirmAction(
      `Apply the "${selectedPreset.name}" preset? This will overwrite your current error handling configuration.`
    );

    if (!confirmed) {
      return;
    }

    try {
      await this.configManager.updateErrorHandlingConfig(selectedPreset.config);
      this.utils.showSuccess(`✅ "${selectedPreset.name}" preset applied successfully!`);
    } catch (error) {
      this.utils.showError(`Failed to apply preset: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async saveCurrentAsPreset(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('💾 Save Current Configuration as Preset');

    const currentConfig = this.configManager.getConfig().errorHandling || {};

    this.utils.showInfo('Current configuration:');
    console.log();
    this.displayConfig(currentConfig);
    console.log();

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'name',
        message: 'Preset name:',
        validate: (input) => {
          if (!input.trim()) return 'Name is required';
          if (this.presets.some(p => p.name.toLowerCase() === input.toLowerCase())) {
            return 'A built-in preset with this name already exists';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'description',
        message: 'Preset description:',
        validate: (input) => input.trim() ? true : 'Description is required',
      },
    ]);

    try {
      await this.saveCustomPreset({
        name: answers.name,
        description: answers.description,
        config: currentConfig,
      });
      this.utils.showSuccess(`✅ Preset "${answers.name}" saved successfully!`);
    } catch (error) {
      this.utils.showError(`Failed to save preset: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async deleteCustomPreset(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🗑️ Delete Custom Preset');

    const customPresets = await this.getCustomPresets();
    
    if (customPresets.length === 0) {
      this.utils.showInfo('No custom presets found.');
      await this.utils.waitForKeyPress();
      return;
    }

    const choices = customPresets.map(preset => ({
      name: `${preset.name} - ${preset.description}`,
      value: preset,
    }));

    const { selectedPreset } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedPreset',
        message: 'Select a preset to delete:',
        choices,
      },
    ]);

    const confirmed = await this.confirmAction(
      `Delete the "${selectedPreset.name}" preset? This action cannot be undone.`
    );

    if (!confirmed) {
      return;
    }

    try {
      await this.deleteCustomPresetByName(selectedPreset.name);
      this.utils.showSuccess(`✅ Preset "${selectedPreset.name}" deleted successfully!`);
    } catch (error) {
      this.utils.showError(`Failed to delete preset: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async comparePresets(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📊 Compare Configuration Presets');

    const allPresets = [...this.presets, ...(await this.getCustomPresets())];
    
    if (allPresets.length < 2) {
      this.utils.showInfo('Need at least 2 presets to compare.');
      await this.utils.waitForKeyPress();
      return;
    }

    const choices = allPresets.map(preset => ({
      name: preset.name,
      value: preset,
    }));

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'preset1',
        message: 'Select first preset:',
        choices,
      },
      {
        type: 'list',
        name: 'preset2',
        message: 'Select second preset:',
        choices: (answers: any) => choices.filter(choice => choice.value !== answers.preset1),
      },
    ]);

    this.utils.showInfo(`Comparing "${answers.preset1.name}" vs "${answers.preset2.name}":`);
    console.log();

    // Compare retry settings
    console.log('🔄 Retry Settings:');
    console.log(`  Max Attempts: ${answers.preset1.config.retry?.maxAttempts || 'N/A'} vs ${answers.preset2.config.retry?.maxAttempts || 'N/A'}`);
    console.log(`  Base Delay: ${answers.preset1.config.retry?.baseDelay || 'N/A'}ms vs ${answers.preset2.config.retry?.baseDelay || 'N/A'}ms`);
    console.log(`  Max Delay: ${answers.preset1.config.retry?.maxDelay || 'N/A'}ms vs ${answers.preset2.config.retry?.maxDelay || 'N/A'}ms`);
    console.log();

    // Compare circuit breaker settings
    console.log('⚡ Circuit Breaker Settings:');
    console.log(`  Enabled: ${answers.preset1.config.circuitBreaker?.enabled ? 'Yes' : 'No'} vs ${answers.preset2.config.circuitBreaker?.enabled ? 'Yes' : 'No'}`);
    console.log(`  Failure Threshold: ${answers.preset1.config.circuitBreaker?.failureThreshold || 'N/A'} vs ${answers.preset2.config.circuitBreaker?.failureThreshold || 'N/A'}`);
    console.log(`  Recovery Timeout: ${answers.preset1.config.circuitBreaker?.recoveryTimeout || 'N/A'}ms vs ${answers.preset2.config.circuitBreaker?.recoveryTimeout || 'N/A'}ms`);
    console.log();

    // Compare other settings
    console.log('🎛️ Other Settings:');
    console.log(`  Error Logging: ${answers.preset1.config.enableErrorLogging ? 'Yes' : 'No'} vs ${answers.preset2.config.enableErrorLogging ? 'Yes' : 'No'}`);
    console.log(`  Metrics: ${answers.preset1.config.enableMetrics ? 'Yes' : 'No'} vs ${answers.preset2.config.enableMetrics ? 'Yes' : 'No'}`);
    console.log(`  Progress Display: ${answers.preset1.config.showRetryProgress ? 'Yes' : 'No'} vs ${answers.preset2.config.showRetryProgress ? 'Yes' : 'No'}`);

    await this.utils.waitForKeyPress();
  }

  private async exportPreset(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📤 Export Configuration Preset');

    const allPresets = [...this.presets, ...(await this.getCustomPresets())];
    
    const choices = allPresets.map(preset => ({
      name: `${preset.name} - ${preset.description}`,
      value: preset,
    }));

    const { selectedPreset } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedPreset',
        message: 'Select a preset to export:',
        choices,
      },
    ]);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `error-handling-preset-${selectedPreset.name.toLowerCase().replace(/\s+/g, '-')}-${timestamp}.json`;

    this.utils.showInfo(`Preset would be exported to: ${filename}`);
    console.log();
    console.log('Export content preview:');
    console.log(JSON.stringify(selectedPreset, null, 2));
    
    this.utils.showSuccess('✅ Preset export prepared successfully!');
    this.utils.showInfo('💡 In a real implementation, this would save the file to disk.');

    await this.utils.waitForKeyPress();
  }

  private async importPreset(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📥 Import Configuration Preset');

    this.utils.showInfo('Import a preset from a JSON file.');
    this.utils.showWarning('This feature would allow importing preset files in a real implementation.');
    
    const samplePreset = {
      name: 'Custom Preset',
      description: 'A custom error handling configuration',
      config: {
        retry: {
          maxAttempts: 4,
          baseDelay: 1500,
          maxDelay: 45000,
          backoffMultiplier: 2.2,
          backoffStrategy: 'exponential',
          jitterType: 'full',
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 6,
          recoveryTimeout: 25000,
          successThreshold: 2,
        },
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: true,
        allowRetryCancel: true,
      },
    };

    console.log();
    console.log('Sample preset format:');
    console.log(JSON.stringify(samplePreset, null, 2));

    await this.utils.waitForKeyPress();
  }

  private displayPresetConfig(preset: ErrorHandlingPreset): void {
    this.displayConfig(preset.config);
  }

  private displayConfig(config: any): void {
    if (config.retry) {
      console.log('🔄 Retry Settings:');
      console.log(`  • Max Attempts: ${config.retry.maxAttempts}`);
      console.log(`  • Base Delay: ${config.retry.baseDelay}ms`);
      console.log(`  • Max Delay: ${config.retry.maxDelay}ms`);
      console.log(`  • Backoff Multiplier: ${config.retry.backoffMultiplier}`);
      console.log();
    }

    if (config.circuitBreaker) {
      console.log('⚡ Circuit Breaker Settings:');
      console.log(`  • Enabled: ${config.circuitBreaker.enabled ? 'Yes' : 'No'}`);
      console.log(`  • Failure Threshold: ${config.circuitBreaker.failureThreshold}`);
      console.log(`  • Recovery Timeout: ${config.circuitBreaker.recoveryTimeout}ms`);
      console.log();
    }

    console.log('🎛️ Other Settings:');
    console.log(`  • Error Logging: ${config.enableErrorLogging ? 'Yes' : 'No'}`);
    console.log(`  • Metrics: ${config.enableMetrics ? 'Yes' : 'No'}`);
    console.log(`  • Progress Display: ${config.showRetryProgress ? 'Yes' : 'No'}`);
    console.log(`  • Allow Cancel: ${config.allowRetryCancel ? 'Yes' : 'No'}`);
  }

  private async getCustomPresets(): Promise<ErrorHandlingPreset[]> {
    // In a real implementation, this would load from storage
    return [];
  }

  private async saveCustomPreset(preset: ErrorHandlingPreset): Promise<void> {
    // In a real implementation, this would save to storage
    console.log('Saving custom preset:', preset.name);
  }

  private async deleteCustomPresetByName(name: string): Promise<void> {
    // In a real implementation, this would delete from storage
    console.log('Deleting custom preset:', name);
  }
}
