/**
 * Retry Policy System with Exponential Backoff and Jitter
 * 
 * Provides configurable retry mechanisms with:
 * - Exponential backoff with customizable base delay and multiplier
 * - Jitter implementation (full jitter and decorrelated jitter)
 * - Maximum retry attempts and delay caps
 * - Conditional retry logic based on error types
 */

export interface RetryPolicyConfig {
  /** Maximum number of retry attempts (default: 3) */
  maxAttempts: number;
  
  /** Base delay in milliseconds (default: 1000) */
  baseDelay: number;
  
  /** Backoff multiplier (default: 2) */
  backoffMultiplier: number;
  
  /** Maximum delay cap in milliseconds (default: 60000) */
  maxDelay: number;
  
  /** Enable jitter to prevent thundering herd (default: true) */
  enableJitter: boolean;
  
  /** Jitter type: 'full' | 'decorrelated' (default: 'full') */
  jitterType: 'full' | 'decorrelated';
  
  /** Custom retry condition function */
  shouldRetry?: (error: Error, attempt: number) => boolean;
  
  /** Timeout for each individual attempt in milliseconds */
  attemptTimeout?: number;
}

export interface RetryAttempt {
  attempt: number;
  delay: number;
  error?: Error;
  timestamp: Date;
  duration?: number;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: RetryAttempt[];
  totalDuration: number;
  finalAttempt: number;
}

export class RetryPolicy {
  private config: RetryPolicyConfig;
  private lastDelay: number = 0; // For decorrelated jitter

  constructor(config: Partial<RetryPolicyConfig> = {}) {
    this.config = {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 60000,
      enableJitter: true,
      jitterType: 'full',
      ...config
    };
  }

  /**
   * Execute a function with retry logic
   */
  async execute<T>(
    operation: () => Promise<T>,
    operationName?: string
  ): Promise<RetryResult<T>> {
    const startTime = Date.now();
    const attempts: RetryAttempt[] = [];
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
      const attemptStart = Date.now();
      const attemptInfo: RetryAttempt = {
        attempt,
        delay: 0,
        timestamp: new Date(attemptStart)
      };

      try {
        // Add timeout wrapper if specified
        const result = this.config.attemptTimeout
          ? await this.withTimeout(operation(), this.config.attemptTimeout)
          : await operation();

        attemptInfo.duration = Date.now() - attemptStart;
        attempts.push(attemptInfo);

        return {
          success: true,
          result,
          attempts,
          totalDuration: Date.now() - startTime,
          finalAttempt: attempt
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        attemptInfo.error = lastError;
        attemptInfo.duration = Date.now() - attemptStart;

        // Check if we should retry this error
        if (!this.shouldRetryError(lastError, attempt)) {
          attempts.push(attemptInfo);
          break;
        }

        // If this is the last attempt, don't delay
        if (attempt === this.config.maxAttempts) {
          attempts.push(attemptInfo);
          break;
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt);
        attemptInfo.delay = delay;
        attempts.push(attemptInfo);

        // Wait before next attempt
        await this.delay(delay);
      }
    }

    return {
      success: false,
      error: lastError,
      attempts,
      totalDuration: Date.now() - startTime,
      finalAttempt: attempts.length
    };
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number): number {
    // Calculate base exponential backoff
    const exponentialDelay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1);
    const cappedDelay = Math.min(exponentialDelay, this.config.maxDelay);

    if (!this.config.enableJitter) {
      return cappedDelay;
    }

    return this.applyJitter(cappedDelay);
  }

  /**
   * Apply jitter to delay
   */
  private applyJitter(delay: number): number {
    switch (this.config.jitterType) {
      case 'full':
        // Full jitter: random(0, delay)
        return Math.random() * delay;
      
      case 'decorrelated':
        // Decorrelated jitter: random(baseDelay, lastDelay * 3)
        const min = this.config.baseDelay;
        const max = Math.min(this.lastDelay * 3, this.config.maxDelay);
        const jitteredDelay = Math.random() * (max - min) + min;
        this.lastDelay = jitteredDelay;
        return jitteredDelay;
      
      default:
        return delay;
    }
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetryError(error: Error, attempt: number): boolean {
    // Use custom retry condition if provided
    if (this.config.shouldRetry) {
      return this.config.shouldRetry(error, attempt);
    }

    // Default retry logic - will be enhanced by ErrorClassifier
    return this.isTransientError(error);
  }

  /**
   * Basic transient error detection (will be enhanced by ErrorClassifier)
   */
  private isTransientError(error: Error): boolean {
    const message = error.message.toLowerCase();
    const transientPatterns = [
      'timeout',
      'network',
      'connection',
      'econnreset',
      'enotfound',
      'econnrefused',
      'socket hang up',
      'rate limit',
      'too many requests',
      'service unavailable',
      'internal server error',
      'bad gateway',
      'gateway timeout'
    ];

    return transientPatterns.some(pattern => message.includes(pattern));
  }

  /**
   * Add timeout to a promise
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      promise
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update retry policy configuration
   */
  updateConfig(updates: Partial<RetryPolicyConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Get current configuration
   */
  getConfig(): RetryPolicyConfig {
    return { ...this.config };
  }

  /**
   * Create a new RetryPolicy with different configuration
   */
  static create(config: Partial<RetryPolicyConfig> = {}): RetryPolicy {
    return new RetryPolicy(config);
  }

  /**
   * Predefined retry policies for common scenarios
   */
  static readonly presets = {
    /** Fast retry for quick operations */
    fast: new RetryPolicy({
      maxAttempts: 3,
      baseDelay: 500,
      backoffMultiplier: 1.5,
      maxDelay: 5000
    }),

    /** Standard retry for most operations */
    standard: new RetryPolicy({
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000
    }),

    /** Aggressive retry for critical operations */
    aggressive: new RetryPolicy({
      maxAttempts: 5,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 60000
    }),

    /** Conservative retry for expensive operations */
    conservative: new RetryPolicy({
      maxAttempts: 2,
      baseDelay: 2000,
      backoffMultiplier: 3,
      maxDelay: 30000
    })
  };
}
