/**
 * Provider-specific error handling configurations and utilities
 * 
 * This module provides specialized error handling configurations for different
 * AI providers, taking into account their specific rate limits, error patterns,
 * and service characteristics.
 */

import { ErrorHandlingConfiguration, RetryConfiguration } from '../types';

/**
 * Provider-specific error handling configurations
 */
export const PROVIDER_ERROR_CONFIGS: Record<string, Partial<ErrorHandlingConfiguration>> = {
  openai: {
    retry: {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000,
      enableJitter: true,
      jitterType: 'decorrelated',
      attemptTimeout: 60000
    },
    circuitBreaker: {
      failureThreshold: 5,
      recoveryTimeout: 60000,
      successThreshold: 2,
      monitoringWindow: 300000,
      enabled: true
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: false,
    allowRetryCancel: false,
    operationPolicies: {
      providerCalls: {
        maxAttempts: 3,
        baseDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 30000,
        enableJitter: true,
        jitterType: 'decorrelated'
      }
    }
  },

  anthropic: {
    retry: {
      maxAttempts: 3,
      baseDelay: 1500, // Slightly higher base delay for Anthropic
      backoffMultiplier: 2,
      maxDelay: 45000,
      enableJitter: true,
      jitterType: 'decorrelated',
      attemptTimeout: 90000 // Longer timeout for Claude models
    },
    circuitBreaker: {
      failureThreshold: 4, // Slightly more conservative
      recoveryTimeout: 90000,
      successThreshold: 2,
      monitoringWindow: 300000,
      enabled: true
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: false,
    allowRetryCancel: false,
    operationPolicies: {
      providerCalls: {
        maxAttempts: 3,
        baseDelay: 1500,
        backoffMultiplier: 2,
        maxDelay: 45000,
        enableJitter: true,
        jitterType: 'decorrelated'
      }
    }
  },

  google: {
    retry: {
      maxAttempts: 4, // Google services are generally reliable
      baseDelay: 800,
      backoffMultiplier: 1.8,
      maxDelay: 25000,
      enableJitter: true,
      jitterType: 'full',
      attemptTimeout: 45000
    },
    circuitBreaker: {
      failureThreshold: 6,
      recoveryTimeout: 45000,
      successThreshold: 3,
      monitoringWindow: 300000,
      enabled: true
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: false,
    allowRetryCancel: false,
    operationPolicies: {
      providerCalls: {
        maxAttempts: 4,
        baseDelay: 800,
        backoffMultiplier: 1.8,
        maxDelay: 25000,
        enableJitter: true,
        jitterType: 'full'
      }
    }
  },

  deepseek: {
    retry: {
      maxAttempts: 3,
      baseDelay: 1200,
      backoffMultiplier: 2.2,
      maxDelay: 40000,
      enableJitter: true,
      jitterType: 'decorrelated',
      attemptTimeout: 75000
    },
    circuitBreaker: {
      failureThreshold: 4,
      recoveryTimeout: 75000,
      successThreshold: 2,
      monitoringWindow: 300000,
      enabled: true
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: false,
    allowRetryCancel: false,
    operationPolicies: {
      providerCalls: {
        maxAttempts: 3,
        baseDelay: 1200,
        backoffMultiplier: 2.2,
        maxDelay: 40000,
        enableJitter: true,
        jitterType: 'decorrelated'
      }
    }
  }
};

/**
 * Get error handling configuration for a specific provider
 */
export function getProviderErrorConfig(providerName: string): Partial<ErrorHandlingConfiguration> {
  const config = PROVIDER_ERROR_CONFIGS[providerName.toLowerCase()];
  if (!config) {
    console.warn(`No specific error handling configuration found for provider: ${providerName}. Using default configuration.`);
    return PROVIDER_ERROR_CONFIGS.openai; // Use OpenAI as default
  }
  return config;
}

/**
 * Merge user-provided error configuration with provider defaults
 */
export function mergeProviderErrorConfig(
  providerName: string,
  userConfig?: Partial<ErrorHandlingConfiguration>
): ErrorHandlingConfiguration {
  const providerDefaults = getProviderErrorConfig(providerName);
  
  if (!userConfig) {
    return providerDefaults as ErrorHandlingConfiguration;
  }

  // Deep merge configurations
  const merged: ErrorHandlingConfiguration = {
    retry: {
      ...providerDefaults.retry,
      ...userConfig.retry
    },
    circuitBreaker: {
      ...providerDefaults.circuitBreaker,
      ...userConfig.circuitBreaker
    },
    enableErrorLogging: userConfig.enableErrorLogging ?? providerDefaults.enableErrorLogging ?? true,
    enableMetrics: userConfig.enableMetrics ?? providerDefaults.enableMetrics ?? true,
    showRetryProgress: userConfig.showRetryProgress ?? providerDefaults.showRetryProgress ?? false,
    allowRetryCancel: userConfig.allowRetryCancel ?? providerDefaults.allowRetryCancel ?? false,
    operationPolicies: {
      ...providerDefaults.operationPolicies,
      ...userConfig.operationPolicies,
      providerCalls: {
        ...providerDefaults.operationPolicies?.providerCalls,
        ...userConfig.operationPolicies?.providerCalls
      }
    }
  };

  return merged;
}

/**
 * Provider-specific error classification rules
 */
export const PROVIDER_ERROR_PATTERNS: Record<string, Record<string, RegExp[]>> = {
  openai: {
    rateLimitErrors: [
      /rate limit/i,
      /too many requests/i,
      /quota exceeded/i,
      /429/
    ],
    authenticationErrors: [
      /invalid api key/i,
      /unauthorized/i,
      /401/,
      /incorrect api key/i
    ],
    serviceUnavailableErrors: [
      /service unavailable/i,
      /502/,
      /503/,
      /504/,
      /bad gateway/i,
      /gateway timeout/i
    ],
    networkErrors: [
      /ECONNREFUSED/,
      /ENOTFOUND/,
      /ETIMEDOUT/,
      /network error/i
    ]
  },

  anthropic: {
    rateLimitErrors: [
      /rate limit/i,
      /too many requests/i,
      /429/,
      /rate_limit_error/i
    ],
    authenticationErrors: [
      /invalid api key/i,
      /unauthorized/i,
      /401/,
      /authentication_error/i
    ],
    serviceUnavailableErrors: [
      /service unavailable/i,
      /502/,
      /503/,
      /504/,
      /overloaded_error/i,
      /api_error/i
    ],
    networkErrors: [
      /ECONNREFUSED/,
      /ENOTFOUND/,
      /ETIMEDOUT/,
      /network error/i
    ]
  },

  google: {
    rateLimitErrors: [
      /quota exceeded/i,
      /rate limit/i,
      /429/,
      /RATE_LIMIT_EXCEEDED/
    ],
    authenticationErrors: [
      /invalid api key/i,
      /unauthorized/i,
      /401/,
      /UNAUTHENTICATED/
    ],
    serviceUnavailableErrors: [
      /service unavailable/i,
      /502/,
      /503/,
      /504/,
      /UNAVAILABLE/,
      /INTERNAL/
    ],
    networkErrors: [
      /ECONNREFUSED/,
      /ENOTFOUND/,
      /ETIMEDOUT/,
      /network error/i
    ]
  },

  deepseek: {
    rateLimitErrors: [
      /rate limit/i,
      /too many requests/i,
      /429/,
      /quota exceeded/i
    ],
    authenticationErrors: [
      /invalid api key/i,
      /unauthorized/i,
      /401/,
      /authentication failed/i
    ],
    serviceUnavailableErrors: [
      /service unavailable/i,
      /502/,
      /503/,
      /504/,
      /server error/i
    ],
    networkErrors: [
      /ECONNREFUSED/,
      /ENOTFOUND/,
      /ETIMEDOUT/,
      /network error/i
    ]
  }
};

/**
 * Check if an error matches a specific pattern for a provider
 */
export function matchesProviderErrorPattern(
  providerName: string,
  error: Error,
  patternType: 'rateLimitErrors' | 'authenticationErrors' | 'serviceUnavailableErrors' | 'networkErrors'
): boolean {
  const patterns = PROVIDER_ERROR_PATTERNS[providerName.toLowerCase()]?.[patternType];
  if (!patterns) {
    return false;
  }

  const errorMessage = error.message.toLowerCase();
  return patterns.some(pattern => pattern.test(errorMessage));
}

/**
 * Get recommended retry delay for provider-specific rate limit errors
 */
export function getProviderRateLimitDelay(providerName: string, error: Error): number {
  // Extract retry-after header or use provider-specific defaults
  const errorMessage = error.message.toLowerCase();
  
  // Try to extract retry-after from error message
  const retryAfterMatch = errorMessage.match(/retry after (\d+)/i);
  if (retryAfterMatch) {
    return parseInt(retryAfterMatch[1]) * 1000; // Convert to milliseconds
  }

  // Provider-specific default delays for rate limits
  const defaultDelays: Record<string, number> = {
    openai: 60000,     // 1 minute
    anthropic: 90000,  // 1.5 minutes
    google: 45000,     // 45 seconds
    deepseek: 75000    // 1.25 minutes
  };

  return defaultDelays[providerName.toLowerCase()] || 60000;
}
