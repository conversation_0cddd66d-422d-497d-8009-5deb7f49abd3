import axios, { AxiosInstance } from 'axios';
import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';

export class DeepseekProvider extends BaseProvider {
  public name = 'deepseek';
  private client: AxiosInstance;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateConfig();
    
    this.client = axios.create({
      baseURL: this.config.baseUrl || 'https://api.deepseek.com/v1',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 60000, // 60 seconds
    });
  }

  public async sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const formattedMessages = this.formatMessages(messages);
      const formattedTools = this.formatTools(tools);

      const requestData: any = {
        model: requestOptions.model,
        messages: formattedMessages,
        max_tokens: requestOptions.maxTokens,
        temperature: requestOptions.temperature,
        stream: false,
      };

      if (this.config.systemPrompt) {
        requestData.messages.unshift({
          role: 'system',
          content: this.config.systemPrompt,
        });
      }

      if (formattedTools.length > 0) {
        requestData.tools = formattedTools;
        requestData.tool_choice = 'auto';
      }

      const response = await this.client.post('/chat/completions', requestData);
      
      return this.processResponse(response.data);
    } catch (error) {
      throw this.handleError(error, 'sendMessage');
    }
  }

  public async *streamMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const formattedMessages = this.formatMessages(messages);
      const formattedTools = this.formatTools(tools);

      const requestData: any = {
        model: requestOptions.model,
        messages: formattedMessages,
        max_tokens: requestOptions.maxTokens,
        temperature: requestOptions.temperature,
        stream: true,
      };

      if (this.config.systemPrompt) {
        requestData.messages.unshift({
          role: 'system',
          content: this.config.systemPrompt,
        });
      }

      if (formattedTools.length > 0) {
        requestData.tools = formattedTools;
        requestData.tool_choice = 'auto';
      }

      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream',
      });

      let fullContent = '';
      let toolCalls: any[] = [];
      let usage: any = null;

      const stream = response.data;
      
      for await (const chunk of this.parseSSEStream(stream)) {
        if (chunk.choices && chunk.choices[0]) {
          const delta = chunk.choices[0].delta;
          
          if (delta.content) {
            fullContent += delta.content;
            yield delta.content;
          }

          if (delta.tool_calls) {
            toolCalls.push(...delta.tool_calls);
          }
        }

        if (chunk.usage) {
          usage = chunk.usage;
        }
      }

      // Debug: Log tool call processing for streaming
      if (toolCalls.length > 0) {
        console.log(`DeepseekProvider (stream): Found ${toolCalls.length} tool calls in response`);
        console.log('DeepseekProvider (stream): Tool calls:', toolCalls.map(tc => tc.name));
      }

      const formattedToolCalls = toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined;

      return this.createChatMessage(fullContent, 'assistant', {
        provider: this.name,
        model: requestOptions.model,
        usage: usage ? {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
        } : undefined,
        toolCalls: formattedToolCalls, // Put toolCalls in metadata at top level for consistency
      });
    } catch (error) {
      throw this.handleError(error, 'streamMessage');
    }
  }

  private async *parseSSEStream(stream: any): AsyncGenerator<any> {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            yield parsed;
          } catch (error) {
            // Skip invalid JSON
            continue;
          }
        }
      }
    }
  }

  private processResponse(response: any): ChatMessage {
    const choice = response.choices[0];
    const message = choice.message;

    const content = message.content || '';
    const toolCalls = message.tool_calls ? this.formatToolCalls(message.tool_calls) : undefined;

    // Debug: Log tool call processing
    if (toolCalls && toolCalls.length > 0) {
      console.log(`DeepseekProvider: Found ${toolCalls.length} tool calls in response`);
      console.log('DeepseekProvider: Tool calls:', toolCalls.map(tc => tc.name));
    }

    return this.createChatMessage(content, 'assistant', {
      provider: this.name,
      model: response.model,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
      toolCalls, // Put toolCalls in metadata at top level for consistency
      finishReason: choice.finish_reason,
    });
  }

  private formatToolCalls(toolCalls: any[]): any[] {
    return toolCalls.map(call => ({
      id: call.id,
      name: call.function.name,
      parameters: JSON.parse(call.function.arguments || '{}'),
    }));
  }

  protected formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formatted: any = {
        role: msg.role,
        content: msg.content,
      };

      // Handle tool calls in assistant messages
      if (msg.role === 'assistant' && msg.toolCalls) {
        formatted.tool_calls = msg.toolCalls.map((call: any) => ({
          id: call.id,
          type: 'function',
          function: {
            name: call.name,
            arguments: JSON.stringify(call.parameters),
          },
        }));
      }

      // Handle tool responses
      if (msg.role === 'tool') {
        formatted.tool_call_id = msg.metadata?.toolCallId;
      }

      return formatted;
    });
  }

  public async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/models');
      return response.data && response.data.data && response.data.data.length > 0;
    } catch (error) {
      console.error('Deepseek connection test failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data
        .filter((model: any) => model.id.includes('deepseek'))
        .map((model: any) => model.id)
        .sort();
    } catch (error) {
      console.error('Failed to fetch Deepseek models:', error);
      return ['deepseek-chat', 'deepseek-coder']; // Fallback to known models
    }
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.baseUrl) {
      this.config.baseUrl = 'https://api.deepseek.com/v1';
    }
  }

  protected handleError(error: any, context: string): Error {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 401:
          return new Error('Deepseek API authentication failed. Check your API key.');
        case 403:
          return new Error('Deepseek API access forbidden. Check your API permissions.');
        case 429:
          return new Error('Deepseek API rate limit exceeded. Please try again later.');
        case 500:
          return new Error('Deepseek API server error. Please try again later.');
        default:
          const message = data?.error?.message || error.message;
          return new Error(`Deepseek API error (${status}): ${message}`);
      }
    }
    
    return super.handleError(error, context);
  }
}
